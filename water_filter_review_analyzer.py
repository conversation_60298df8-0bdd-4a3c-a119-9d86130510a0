#!/usr/bin/env python3
"""
净水器评论分析工具
专门针对Amazon净水器商品评论的获取和分析
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
from datetime import datetime
import numpy as np
from textblob import TextBlob
import warnings
warnings.filterwarnings('ignore')

class WaterFilterReviewAnalyzer:
    """净水器评论分析器"""
    
    def __init__(self, reviews_file: str):
        """初始化分析器"""
        self.reviews_file = reviews_file
        self.reviews_df = None
        self.load_reviews()
        
    def load_reviews(self):
        """加载评论数据"""
        try:
            if self.reviews_file.endswith('.json'):
                with open(self.reviews_file, 'r', encoding='utf-8') as f:
                    reviews_data = json.load(f)
                self.reviews_df = pd.DataFrame(reviews_data)
            elif self.reviews_file.endswith('.csv'):
                self.reviews_df = pd.read_csv(self.reviews_file)
            
            print(f"成功加载 {len(self.reviews_df)} 条评论")
            
        except Exception as e:
            print(f"加载评论文件失败: {e}")
            
    def basic_statistics(self):
        """基础统计分析"""
        if self.reviews_df is None:
            return
            
        print("\n=== 基础统计信息 ===")
        print(f"总评论数: {len(self.reviews_df)}")
        print(f"平均评分: {self.reviews_df['rating'].mean():.2f}")
        print(f"评分中位数: {self.reviews_df['rating'].median():.2f}")
        print(f"验证购买比例: {self.reviews_df['verified_purchase'].mean()*100:.1f}%")
        
        # 评分分布
        print("\n评分分布:")
        rating_counts = self.reviews_df['rating'].value_counts().sort_index()
        for rating, count in rating_counts.items():
            percentage = count / len(self.reviews_df) * 100
            print(f"{rating}星: {count}条 ({percentage:.1f}%)")
            
    def sentiment_analysis(self):
        """情感分析"""
        print("\n=== 情感分析 ===")
        
        sentiments = []
        for content in self.reviews_df['content']:
            if pd.notna(content):
                blob = TextBlob(str(content))
                sentiments.append(blob.sentiment.polarity)
            else:
                sentiments.append(0)
                
        self.reviews_df['sentiment'] = sentiments
        
        # 情感分类
        def classify_sentiment(score):
            if score > 0.1:
                return '正面'
            elif score < -0.1:
                return '负面'
            else:
                return '中性'
                
        self.reviews_df['sentiment_label'] = self.reviews_df['sentiment'].apply(classify_sentiment)
        
        sentiment_counts = self.reviews_df['sentiment_label'].value_counts()
        print("情感分布:")
        for sentiment, count in sentiment_counts.items():
            percentage = count / len(self.reviews_df) * 100
            print(f"{sentiment}: {count}条 ({percentage:.1f}%)")
            
    def extract_keywords(self, min_length=3, top_n=20):
        """提取关键词"""
        print(f"\n=== 关键词分析 (Top {top_n}) ===")
        
        # 净水器相关的停用词
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
            'a', 'an', 'as', 'if', 'so', 'than', 'very', 'can', 'just', 'get', 'got',
            'one', 'two', 'three', 'first', 'also', 'well', 'good', 'great', 'nice'
        }
        
        all_text = " ".join([str(content) for content in self.reviews_df['content'] if pd.notna(content)])
        
        # 提取单词
        words = re.findall(r'\b[a-zA-Z]+\b', all_text.lower())
        words = [word for word in words if len(word) >= min_length and word not in stop_words]
        
        keyword_counts = Counter(words).most_common(top_n)
        
        print("高频关键词:")
        for word, count in keyword_counts:
            print(f"{word}: {count}次")
            
        return keyword_counts
        
    def analyze_by_rating(self):
        """按评分分析"""
        print("\n=== 按评分分析 ===")
        
        for rating in sorted(self.reviews_df['rating'].unique()):
            rating_reviews = self.reviews_df[self.reviews_df['rating'] == rating]
            print(f"\n{rating}星评论 ({len(rating_reviews)}条):")
            
            # 提取该评分下的关键词
            rating_text = " ".join([str(content) for content in rating_reviews['content'] if pd.notna(content)])
            words = re.findall(r'\b[a-zA-Z]+\b', rating_text.lower())
            
            # 净水器特定关键词
            filter_keywords = ['filter', 'water', 'taste', 'quality', 'clean', 'installation', 
                             'easy', 'difficult', 'leak', 'pressure', 'flow', 'cartridge', 
                             'replacement', 'price', 'value', 'recommend', 'problem', 'issue']
            
            relevant_words = [word for word in words if word in filter_keywords]
            if relevant_words:
                top_words = Counter(relevant_words).most_common(5)
                print(f"  关键词: {', '.join([f'{word}({count})' for word, count in top_words])}")
                
    def find_common_issues(self):
        """发现常见问题"""
        print("\n=== 常见问题分析 ===")
        
        # 问题关键词
        issue_keywords = {
            '安装问题': ['install', 'installation', 'difficult', 'hard', 'complex', 'confusing'],
            '漏水问题': ['leak', 'leaking', 'drip', 'dripping', 'water damage'],
            '水压问题': ['pressure', 'flow', 'slow', 'weak', 'low pressure'],
            '味道问题': ['taste', 'smell', 'odor', 'bad taste', 'metallic', 'plastic'],
            '质量问题': ['quality', 'cheap', 'flimsy', 'break', 'broken', 'defective'],
            '价格问题': ['expensive', 'overpriced', 'cheap', 'value', 'money', 'cost'],
            '更换问题': ['replacement', 'cartridge', 'filter change', 'maintenance']
        }
        
        low_rating_reviews = self.reviews_df[self.reviews_df['rating'] <= 2]
        all_negative_text = " ".join([str(content) for content in low_rating_reviews['content'] if pd.notna(content)]).lower()
        
        print("低评分评论中的常见问题:")
        for issue_type, keywords in issue_keywords.items():
            count = sum(all_negative_text.count(keyword) for keyword in keywords)
            if count > 0:
                print(f"{issue_type}: 提及{count}次")
                
    def find_positive_aspects(self):
        """发现积极方面"""
        print("\n=== 积极方面分析 ===")
        
        positive_keywords = {
            '水质改善': ['clean', 'pure', 'clear', 'fresh', 'better taste', 'good taste'],
            '安装简单': ['easy install', 'simple', 'straightforward', 'quick setup'],
            '性价比': ['value', 'worth', 'good price', 'affordable', 'reasonable'],
            '效果显著': ['effective', 'works well', 'great results', 'noticeable difference'],
            '质量可靠': ['quality', 'durable', 'solid', 'well made', 'reliable'],
            '客服满意': ['customer service', 'support', 'helpful', 'responsive']
        }
        
        high_rating_reviews = self.reviews_df[self.reviews_df['rating'] >= 4]
        all_positive_text = " ".join([str(content) for content in high_rating_reviews['content'] if pd.notna(content)]).lower()
        
        print("高评分评论中的积极方面:")
        for aspect_type, keywords in positive_keywords.items():
            count = sum(all_positive_text.count(keyword) for keyword in keywords)
            if count > 0:
                print(f"{aspect_type}: 提及{count}次")
                
    def generate_summary(self):
        """生成购买建议摘要"""
        print("\n" + "="*50)
        print("🎯 购买建议摘要")
        print("="*50)
        
        avg_rating = self.reviews_df['rating'].mean()
        total_reviews = len(self.reviews_df)
        verified_ratio = self.reviews_df['verified_purchase'].mean()
        
        print(f"📊 总体评价: {avg_rating:.1f}/5.0 星 (基于{total_reviews}条评论)")
        print(f"✅ 验证购买比例: {verified_ratio*100:.1f}%")
        
        # 推荐度
        if avg_rating >= 4.0:
            recommendation = "🟢 推荐购买"
        elif avg_rating >= 3.0:
            recommendation = "🟡 谨慎考虑"
        else:
            recommendation = "🔴 不推荐"
            
        print(f"💡 推荐度: {recommendation}")
        
        # 主要优点和缺点
        high_rating_count = len(self.reviews_df[self.reviews_df['rating'] >= 4])
        low_rating_count = len(self.reviews_df[self.reviews_df['rating'] <= 2])
        
        print(f"\n📈 满意用户: {high_rating_count}人 ({high_rating_count/total_reviews*100:.1f}%)")
        print(f"📉 不满意用户: {low_rating_count}人 ({low_rating_count/total_reviews*100:.1f}%)")
        
        print("\n💭 购买建议:")
        if avg_rating >= 4.0:
            print("- 这款净水器获得了大多数用户的好评")
            print("- 建议关注安装和维护说明")
            print("- 可以考虑购买")
        elif avg_rating >= 3.0:
            print("- 这款净水器评价一般，有一定争议")
            print("- 建议仔细阅读负面评论中提到的问题")
            print("- 考虑是否有更好的替代品")
        else:
            print("- 这款净水器评价较差，问题较多")
            print("- 建议寻找其他替代产品")
            print("- 如果一定要购买，请做好心理准备")
            
    def create_visualizations(self):
        """创建可视化图表"""
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('default')
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 评分分布
        self.reviews_df['rating'].value_counts().sort_index().plot(kind='bar', ax=axes[0,0])
        axes[0,0].set_title('评分分布')
        axes[0,0].set_xlabel('评分')
        axes[0,0].set_ylabel('数量')
        
        # 情感分布
        if 'sentiment_label' in self.reviews_df.columns:
            self.reviews_df['sentiment_label'].value_counts().plot(kind='pie', ax=axes[0,1], autopct='%1.1f%%')
            axes[0,1].set_title('情感分布')
        
        # 验证购买vs评分
        verified_ratings = self.reviews_df.groupby(['verified_purchase', 'rating']).size().unstack(fill_value=0)
        verified_ratings.plot(kind='bar', ax=axes[1,0])
        axes[1,0].set_title('验证购买vs评分')
        axes[1,0].set_xlabel('是否验证购买')
        axes[1,0].legend(title='评分')
        
        # 评分趋势（如果有日期数据）
        if 'date' in self.reviews_df.columns:
            try:
                self.reviews_df['date'] = pd.to_datetime(self.reviews_df['date'], errors='coerce')
                monthly_avg = self.reviews_df.groupby(self.reviews_df['date'].dt.to_period('M'))['rating'].mean()
                monthly_avg.plot(ax=axes[1,1])
                axes[1,1].set_title('评分趋势')
                axes[1,1].set_xlabel('月份')
                axes[1,1].set_ylabel('平均评分')
            except:
                axes[1,1].text(0.5, 0.5, '无法解析日期数据', ha='center', va='center', transform=axes[1,1].transAxes)
        
        plt.tight_layout()
        plt.savefig('water_filter_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 图表已保存为 'water_filter_analysis.png'")

def main():
    """主函数"""
    print("🚰 净水器评论分析工具")
    print("="*50)
    
    # 这里假设你已经有了评论数据文件
    # 如果没有，请先运行 amazon_review_scraper.py 获取数据
    
    import os
    json_files = [f for f in os.listdir('.') if f.startswith('amazon_reviews_') and f.endswith('.json')]
    
    if not json_files:
        print("❌ 未找到评论数据文件")
        print("请先运行 amazon_review_scraper.py 获取评论数据")
        return
    
    # 使用最新的评论文件
    latest_file = max(json_files, key=os.path.getctime)
    print(f"📁 使用评论文件: {latest_file}")
    
    # 创建分析器
    analyzer = WaterFilterReviewAnalyzer(latest_file)
    
    # 执行分析
    analyzer.basic_statistics()
    analyzer.sentiment_analysis()
    analyzer.extract_keywords()
    analyzer.analyze_by_rating()
    analyzer.find_common_issues()
    analyzer.find_positive_aspects()
    analyzer.generate_summary()
    
    # 创建可视化图表
    try:
        analyzer.create_visualizations()
    except Exception as e:
        print(f"⚠️ 创建图表失败: {e}")
        print("请确保安装了 matplotlib 和 seaborn")

if __name__ == "__main__":
    main()
