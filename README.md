# Amazon评论获取工具

这个工具可以帮你获取Amazon商品的所有评论，支持多种方案，从合法合规到技术实现都有考虑。

## 🎯 最佳方案推荐

### 1. **Rainforest API (强烈推荐)**
- ✅ **合法合规**: 官方API，不违反服务条款
- ✅ **稳定可靠**: 专业服务，99.9%可用性
- ✅ **数据完整**: 获取所有评论字段
- ✅ **无需维护**: 不用担心反爬虫更新
- 💰 **成本**: 约$0.001-0.01/请求，性价比高

**注册地址**: https://www.rainforestapi.com/
**免费额度**: 100次请求/月

### 2. **ScrapFly API (备选)**
- ✅ 专业抓取服务
- ✅ 自动处理反爬虫
- 💰 成本稍高

### 3. **Selenium自动化 (最后选择)**
- ⚠️ 可能被检测和封禁
- ⚠️ 需要维护和更新
- ⚠️ 速度较慢
- ✅ 免费使用

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python amazon_review_scraper.py
```

## 📊 数据输出格式

程序会生成两种格式的文件：

### CSV格式 (适合Excel分析)
```csv
reviewer_name,rating,title,content,date,verified_purchase,helpful_votes
John Doe,5.0,Great product!,This water filter works amazing...,2024-01-15,True,12
```

### JSON格式 (适合程序处理)
```json
[
  {
    "reviewer_name": "John Doe",
    "rating": 5.0,
    "title": "Great product!",
    "content": "This water filter works amazing...",
    "date": "2024-01-15",
    "verified_purchase": true,
    "helpful_votes": 12
  }
]
```

## 🔧 配置说明

### 获取ASIN
从你的Amazon链接中提取ASIN：
```
https://www.amazon.nl/product-reviews/B0DG5QLWP6/...
                                    ^^^^^^^^^^
                                    这就是ASIN
```

### API密钥配置
1. **Rainforest API**: 注册后在Dashboard获取API Key
2. **ScrapFly API**: 注册后获取API Key

## 📈 评论分析建议

获取评论后，你可以进行以下分析：

### 1. 情感分析
```python
# 可以使用以下库进行情感分析
# pip install textblob vaderSentiment
from textblob import TextBlob

def analyze_sentiment(review_text):
    blob = TextBlob(review_text)
    return blob.sentiment.polarity  # -1到1，越高越正面
```

### 2. 关键词提取
```python
# 提取评论中的关键词
from collections import Counter
import re

def extract_keywords(reviews):
    all_text = " ".join([r.content for r in reviews])
    words = re.findall(r'\b\w+\b', all_text.lower())
    return Counter(words).most_common(20)
```

### 3. 评分分布分析
```python
import pandas as pd
import matplotlib.pyplot as plt

def analyze_ratings(reviews):
    df = pd.DataFrame([r.__dict__ for r in reviews])
    rating_counts = df['rating'].value_counts().sort_index()
    
    plt.figure(figsize=(10, 6))
    rating_counts.plot(kind='bar')
    plt.title('评分分布')
    plt.xlabel('评分')
    plt.ylabel('数量')
    plt.show()
```

## ⚖️ 法律和道德考虑

1. **遵守服务条款**: 推荐使用API方案
2. **合理使用**: 不要过度频繁请求
3. **数据用途**: 仅用于个人决策，不用于商业竞争
4. **隐私保护**: 不要泄露评论者个人信息

## 🛠️ 故障排除

### 常见问题

1. **Selenium找不到Chrome驱动**
   ```bash
   pip install webdriver-manager
   ```

2. **请求被拒绝**
   - 降低请求频率
   - 更换User-Agent
   - 使用代理IP

3. **API配额用完**
   - 检查API使用量
   - 升级API套餐

## 💡 使用建议

1. **首选API方案**: 虽然需要付费，但稳定可靠
2. **批量处理**: 如果有多个商品，可以批量获取
3. **定期更新**: 评论会持续更新，可以定期重新获取
4. **数据备份**: 及时保存获取的数据

## 📞 技术支持

如果遇到问题，可以：
1. 检查网络连接
2. 验证API密钥
3. 查看日志输出
4. 更新依赖包版本

---

**免责声明**: 请确保你的使用符合Amazon的服务条款和当地法律法规。建议优先使用官方API或第三方合规服务。
